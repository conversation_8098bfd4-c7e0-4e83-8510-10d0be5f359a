// ========================================
// HIGH-QUALITY IMAGE GENERATION SYSTEM
// ========================================

class HighQualityImageGenerator {
    constructor() {
        this.isGenerating = false;
        this.lastGeneratedImage = null;
        // 🔥 UPGRADED: Ultra high-quality settings for 3200×1600+ resolution
        this.exportOptions = {
            // Ultra high quality settings for 3200×1600+ output
            quality: 1.0,  // 100% quality for maximum resolution
            pixelRatio: Math.max(4, window.devicePixelRatio || 1), // 🔥 4x on doubled base = ultra quality
            backgroundColor: 'transparent',
            cacheBust: true,

            // Use updated high-resolution canvas dimensions with ultra high multiplier
            width: 1600,   // 🔥 DOUBLED base width for ultra quality
            height: 800,   // 🔥 DOUBLED base height for ultra quality
            canvasWidth: 1600 * 4,   // 🔥 4x multiplier on doubled base = 6400×3200 (exceeds target)
            canvasHeight: 800 * 4,   // 🔥 4x multiplier on doubled base = ultra quality

            // Exact style settings from both export systems
            style: {
                position: 'relative',
                overflow: 'visible',
                transform: 'none',
                transformOrigin: 'top left',
                width: '800px',
                height: '400px',
                maxWidth: 'none',
                minWidth: 'none',
                minHeight: 'none',
                contain: 'none',
                boxSizing: 'border-box',
                margin: '0',
                padding: '0'
            },

            // Filter out UI elements (matching both systems)
            filter: (node) => {
                if (node.classList) {
                    return !node.classList.contains('cf7-delete-btn') &&
                           !node.classList.contains('cf7-resize-handle') &&
                           !node.classList.contains('cf7-selected') &&
                           !node.classList.contains('cf7-editing') &&
                           !node.classList.contains('text-customize-btn') &&
                           !node.classList.contains('selected') &&
                           !node.hasAttribute('data-cf7-ui-element') &&
                           !node.hasAttribute('data-ui-element');
                }
                return true;
            },

            // Handle CORS and fonts gracefully (avoid Google Fonts CORS issues)
            fontEmbedCSS: false,  // Disable font embedding to avoid CORS errors
            includeQueryParams: false,
            skipAutoScale: false,
            skipFonts: true,      // Skip font processing to avoid CORS
            useCORS: false,
            allowTaint: true,     // Allow tainted canvas for better compatibility
            imagePlaceholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJhY2tncm91bmQgSW1hZ2U8L3RleHQ+PC9zdmc+'
        };
    }
    
    /**
     * Generate and save high-quality image from canvas (ultra high quality matching both systems)
     */
    async generateAndSaveImage(canvasElement, options = {}) {
        if (this.isGenerating) {
            throw new Error('Image generation already in progress');
        }

        try {
            this.isGenerating = true;

            // Detect actual canvas dimensions
            const actualDimensions = this.detectCanvasDimensions(canvasElement);

            // 🔥 UPGRADED: Super high quality export options for both mobile and desktop
            // Start with 6x multiplier for super quality (up to 5MB)
            let qualityMultiplier = 6;
            const exportOptions = {
                ...this.exportOptions,
                width: actualDimensions.width,
                height: actualDimensions.height,
                canvasWidth: actualDimensions.width * qualityMultiplier,  // 6x for super quality
                canvasHeight: actualDimensions.height * qualityMultiplier, // 6x for super quality
                style: {
                    ...this.exportOptions.style,
                    width: actualDimensions.width + 'px',
                    height: actualDimensions.height + 'px'
                },
                ...options
            };

            // Show progress
            this.showProgress('Preparing ultra high-quality export...');

            // Preload fonts to avoid CORS issues
            await this.preloadFonts(canvasElement);

            // Prepare canvas for export
            await this.prepareCanvasForExport(canvasElement);

            // Generate high-quality image with retry mechanism
            let imageData;
            let optimizedImageData;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts) {
                try {
                    attempts++;
                    const currentMultiplier = qualityMultiplier / attempts; // Reduce quality on retry
                    const currentOptions = {
                        ...exportOptions,
                        canvasWidth: actualDimensions.width * currentMultiplier,
                        canvasHeight: actualDimensions.height * currentMultiplier
                    };

                    this.showProgress(`Capturing image at ${currentMultiplier}x resolution (attempt ${attempts})...`);
                    imageData = await this.captureHighQualityImage(canvasElement, currentOptions);

                    // Optimize image size for server upload
                    this.showProgress('Optimizing image for upload...');
                    optimizedImageData = await this.optimizeImageForUpload(imageData);

                    // 🔥 UPDATED: Check if file size is within super high quality range (1-5MB)
                    const finalSizeMB = (optimizedImageData.length * 0.75) / (1024 * 1024);
                    if (finalSizeMB <= 25 && finalSizeMB >= 0.5) { // Accept 0.5MB to 25MB range
                        this.showProgress(`✅ SUPER HIGH QUALITY image ready (${finalSizeMB.toFixed(1)}MB)`);
                        break;
                    } else if (attempts < maxAttempts) {
                        this.showProgress(`🔄 Optimizing quality (${finalSizeMB.toFixed(1)}MB), attempt ${attempts}...`);
                        continue;
                    }

                } catch (error) {
                    if (attempts >= maxAttempts) {
                        throw error;
                    }
                    this.showProgress(`Attempt ${attempts} failed, retrying with lower quality...`);
                }
            }

            // Get order data for saving
            const orderData = this.getOrderDataForSaving();

            // Save image to server
            this.showProgress('Saving ultra high-quality image...');
            const saveResult = await this.saveImageToServer(optimizedImageData, orderData);

            // Update order data manager
            if (window.orderDataManager && saveResult.success) {
                window.orderDataManager.setGeneratedImage(
                    saveResult.image.url,
                    saveResult.image.path
                );
            }

            this.lastGeneratedImage = saveResult.image;
            this.showProgress('Ultra high-quality image saved successfully!');

            // Hide progress after delay
            setTimeout(() => this.hideProgress(), 2000);

            return saveResult;

        } catch (error) {
            console.error('Image generation failed:', error);
            this.showProgress('Image generation failed: ' + error.message, 'error');
            setTimeout(() => this.hideProgress(), 3000);
            throw error;
        } finally {
            this.isGenerating = false;
            this.restoreCanvasAfterExport(canvasElement);
        }
    }

    /**
     * Optimize image for upload to avoid PHP size limits
     */
    async optimizeImageForUpload(imageData) {
        try {
            // Check if image data is too large (>40MB base64 ≈ 30MB binary)
            const sizeInMB = (imageData.length * 0.75) / (1024 * 1024); // Approximate binary size

            if (sizeInMB <= 30) {
                // Image is acceptable size, return as-is
                return imageData;
            }

            this.showProgress(`Compressing large image (${sizeInMB.toFixed(1)}MB)...`);

            // Create canvas to re-compress the image
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            return new Promise((resolve) => {
                img.onload = () => {
                    // Set canvas to image dimensions
                    canvas.width = img.width;
                    canvas.height = img.height;

                    // Draw image to canvas
                    ctx.drawImage(img, 0, 0);

                    // Try different compression levels
                    let quality = 0.85; // Start with 85% quality
                    let compressedData;

                    do {
                        compressedData = canvas.toDataURL('image/jpeg', quality);
                        const compressedSizeMB = (compressedData.length * 0.75) / (1024 * 1024);

                        if (compressedSizeMB <= 30) {
                            break;
                        }

                        quality -= 0.1; // Reduce quality by 10%
                    } while (quality > 0.3); // Don't go below 30% quality

                    resolve(compressedData);
                };

                img.src = imageData;
            });

        } catch (error) {
            console.warn('Image optimization failed, using original:', error);
            return imageData;
        }
    }

    /**
     * Detect actual canvas dimensions (matching both billboard systems)
     */
    detectCanvasDimensions(canvasElement) {
        // Try to get dimensions from data attributes first (both systems use this)
        let width = parseInt(canvasElement.dataset.width);
        let height = parseInt(canvasElement.dataset.height);

        // Fallback to computed dimensions
        if (!width || !height) {
            const computedStyle = window.getComputedStyle(canvasElement);
            width = parseInt(computedStyle.width) || 800;
            height = parseInt(computedStyle.height) || 400;
        }

        // Ensure minimum dimensions for ultra quality
        width = Math.max(width, 1600);  // 🔥 DOUBLED minimum for ultra quality
        height = Math.max(height, 800);  // 🔥 DOUBLED minimum for ultra quality

        return { width, height };
    }
    
    /**
     * Preload fonts to avoid CORS issues during export
     */
    async preloadFonts(canvasElement) {
        try {
            // Get all elements with custom fonts
            const elementsWithFonts = canvasElement.querySelectorAll('*');
            const fontsToLoad = new Set();

            elementsWithFonts.forEach(element => {
                const computedStyle = window.getComputedStyle(element);
                const fontFamily = computedStyle.fontFamily;

                // Check if it's a Google Font or custom font
                if (fontFamily && !fontFamily.includes('Arial') && !fontFamily.includes('serif') && !fontFamily.includes('sans-serif')) {
                    fontsToLoad.add(fontFamily.replace(/['"]/g, ''));
                }
            });

            // Load fonts using document.fonts API if available
            if (document.fonts && document.fonts.load) {
                const fontPromises = Array.from(fontsToLoad).map(async (fontFamily) => {
                    try {
                        await document.fonts.load(`16px ${fontFamily}`);
                        await document.fonts.load(`bold 16px ${fontFamily}`);
                    } catch (error) {
                        console.warn(`Failed to preload font: ${fontFamily}`, error);
                    }
                });

                await Promise.allSettled(fontPromises);
            }

            // Wait a bit for fonts to be fully loaded
            await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
            console.warn('Font preloading failed, continuing with export:', error);
        }
    }

    /**
     * Capture high-quality image from canvas (matching both billboard systems)
     */
    async captureHighQualityImage(canvasElement, options) {
        // Use html-to-image library for high-quality capture
        if (typeof htmlToImage === 'undefined') {
            throw new Error('html-to-image library not loaded. Please include: https://cdn.jsdelivr.net/npm/html-to-image@1.11.13/dist/html-to-image.js');
        }

        // Always use PNG for maximum quality (matching both systems default)
        const format = options.format || 'png';

        // Use the exact same export options structure as both systems
        const exportOptions = {
            ...this.exportOptions,
            ...options
        };

        let imageData;
        try {
            if (format === 'jpeg' || format === 'jpg') {
                imageData = await htmlToImage.toJpeg(canvasElement, exportOptions);
            } else {
                // Default to PNG for best quality (matching both systems)
                imageData = await htmlToImage.toPng(canvasElement, exportOptions);
            }
        } catch (error) {
            // If font embedding fails, try without font processing
            console.warn('Export with fonts failed, retrying without font embedding:', error);

            const fallbackOptions = {
                ...exportOptions,
                fontEmbedCSS: false,
                skipFonts: true
            };

            if (format === 'jpeg' || format === 'jpg') {
                imageData = await htmlToImage.toJpeg(canvasElement, fallbackOptions);
            } else {
                imageData = await htmlToImage.toPng(canvasElement, fallbackOptions);
            }
        }

        return imageData;
    }
    
    /**
     * Prepare canvas for high-quality export (matching both billboard systems)
     */
    async prepareCanvasForExport(canvasElement) {
        // Store original canvas state to prevent layout shifts (exact match from custom billboard)
        this.originalStyles = {
            width: canvasElement.style.width,
            height: canvasElement.style.height,
            maxWidth: canvasElement.style.maxWidth,
            minWidth: canvasElement.style.minWidth,
            minHeight: canvasElement.style.minHeight,
            transform: canvasElement.style.transform,
            contain: canvasElement.style.contain,
            overflow: canvasElement.style.overflow,
            position: canvasElement.style.position
        };

        // Set export flag for CSS targeting and lock dimensions
        canvasElement.setAttribute('data-exporting', 'true');

        // Force canvas to original dimensions to prevent mobile scaling issues
        const baseWidth = parseInt(canvasElement.dataset.width) || 800;
        const baseHeight = parseInt(canvasElement.dataset.height) || 400;

        // Apply exact export styles from both systems
        canvasElement.style.width = baseWidth + 'px';
        canvasElement.style.height = baseHeight + 'px';
        canvasElement.style.maxWidth = 'none';
        canvasElement.style.minWidth = 'none';
        canvasElement.style.minHeight = 'none';
        canvasElement.style.transform = 'none';
        canvasElement.style.contain = 'none';
        canvasElement.style.overflow = 'visible';
        canvasElement.style.position = 'relative';

        // Clean up visual editing indicators (matching both systems)
        await this.cleanUpEditingIndicators(canvasElement);

        // Handle background images
        await this.prepareBackgroundImages(canvasElement);

        // Allow DOM to update
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    /**
     * Clean up editing indicators (matching both billboard systems)
     */
    async cleanUpEditingIndicators(canvasElement) {
        // Find all editing elements (matching both systems)
        const selectedElements = canvasElement.querySelectorAll('.cf7-selected, .selected');
        const resizeHandles = canvasElement.querySelectorAll('.cf7-resize-handle');
        const deleteButtons = canvasElement.querySelectorAll('.cf7-delete-btn');
        const editingElements = canvasElement.querySelectorAll('.cf7-editing');
        const customizeButtons = canvasElement.querySelectorAll('.text-customize-btn');
        const allDraggableElements = canvasElement.querySelectorAll('.cf7-draggable-text, .cf7-draggable-image');

        // Store original states for restoration
        this.originalElementStates = [];
        const allElements = [...selectedElements, ...resizeHandles, ...deleteButtons, ...editingElements, ...customizeButtons, ...allDraggableElements];

        allElements.forEach(el => {
            this.originalElementStates.push({
                element: el,
                className: el.className,
                display: el.style.display,
                border: el.style.border,
                background: el.style.background,
                cursor: el.style.cursor,
                outline: el.style.outline,
                boxShadow: el.style.boxShadow
            });
        });

        // Remove all editing and selection states
        [...selectedElements, ...editingElements].forEach(el => {
            el.classList.remove('cf7-selected', 'cf7-editing', 'selected');
        });

        // Hide UI control elements
        [...resizeHandles, ...deleteButtons, ...customizeButtons].forEach(el => {
            el.style.display = 'none';
        });

        // Clean up ALL visual editing indicators from draggable elements
        allDraggableElements.forEach(el => {
            // Remove all editor-related classes temporarily
            el.classList.remove('cf7-selected', 'cf7-editing', 'selected');

            // Clear all visual editing styles
            el.style.border = 'none';
            el.style.background = 'transparent';
            el.style.cursor = 'default';
            el.style.outline = 'none';
            el.style.boxShadow = 'none';

            // Add export mode class
            el.classList.add('cf7-export-mode');
        });
    }
    
    /**
     * Restore canvas after export (matching both billboard systems)
     */
    restoreCanvasAfterExport(canvasElement) {
        // Restore original element states
        if (this.originalElementStates) {
            this.originalElementStates.forEach(state => {
                state.element.className = state.className;
                state.element.style.display = state.display;
                state.element.style.border = state.border;
                state.element.style.background = state.background;
                state.element.style.cursor = state.cursor;
                state.element.style.outline = state.outline;
                state.element.style.boxShadow = state.boxShadow;
            });
            this.originalElementStates = null;
        }

        // Remove export mode class from all elements
        const exportModeElements = canvasElement.querySelectorAll('.cf7-export-mode');
        exportModeElements.forEach(el => {
            el.classList.remove('cf7-export-mode');
        });

        // Restore canvas state to prevent layout shifts
        canvasElement.removeAttribute('data-exporting');
        if (this.originalStyles) {
            canvasElement.style.width = this.originalStyles.width;
            canvasElement.style.height = this.originalStyles.height;
            canvasElement.style.maxWidth = this.originalStyles.maxWidth;
            canvasElement.style.minWidth = this.originalStyles.minWidth;
            canvasElement.style.minHeight = this.originalStyles.minHeight;
            canvasElement.style.transform = this.originalStyles.transform;
            canvasElement.style.contain = this.originalStyles.contain;
            canvasElement.style.overflow = this.originalStyles.overflow;
            canvasElement.style.position = this.originalStyles.position;
            this.originalStyles = null;
        }

        // Restore background images if needed
        this.restoreBackgroundImages(canvasElement);
    }
    
    /**
     * Prepare background images for export
     */
    async prepareBackgroundImages(canvasElement) {
        const bgElements = canvasElement.querySelectorAll('[style*="background-image"]');
        this.backgroundImageBackups = [];
        
        for (const element of bgElements) {
            const bgImage = element.style.backgroundImage;
            if (bgImage && bgImage !== 'none') {
                // Store backup
                this.backgroundImageBackups.push({
                    element: element,
                    originalBgImage: bgImage
                });
                
                try {
                    // Convert background image to img element for better export
                    const imageUrl = bgImage.match(/url\(["']?([^"']*)["']?\)/)?.[1];
                    if (imageUrl) {
                        const img = document.createElement('img');
                        img.src = imageUrl;
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.objectFit = 'cover';
                        img.style.position = 'absolute';
                        img.style.top = '0';
                        img.style.left = '0';
                        
                        element.style.backgroundImage = 'none';
                        element.style.position = 'relative';
                        element.appendChild(img);
                    }
                } catch (error) {
                    console.warn('Failed to prepare background image:', error);
                }
            }
        }
    }
    
    /**
     * Restore background images after export
     */
    restoreBackgroundImages(canvasElement) {
        if (this.backgroundImageBackups) {
            this.backgroundImageBackups.forEach(backup => {
                backup.element.style.backgroundImage = backup.originalBgImage;
                // Remove any img elements added for export
                const exportImages = backup.element.querySelectorAll('img');
                exportImages.forEach(img => img.remove());
            });
            this.backgroundImageBackups = null;
        }
    }
    
    /**
     * Get order data for saving
     */
    getOrderDataForSaving() {
        let orderData = {};
        
        // Try to get from order data manager first
        if (window.orderDataManager) {
            const data = window.orderDataManager.getOrderData();
            if (data) {
                orderData = {
                    billboardType: data.billboardType || this.detectBillboardType(),
                    customerEmail: data.customerEmail || '<EMAIL>',
                    customerName: data.customerName || 'Valued Customer',
                    templateId: data.templateId,
                    designData: data.designData
                };
            }
        }
        
        // Fallback to localStorage
        if (!orderData.billboardType) {
            orderData.billboardType = this.detectBillboardType();
        }
        
        if (!orderData.customerEmail) {
            orderData.customerEmail = localStorage.getItem('customerEmail') || '<EMAIL>';
        }
        
        if (!orderData.customerName) {
            orderData.customerName = localStorage.getItem('customerName') || 'Valued Customer';
        }
        
        return orderData;
    }
    
    /**
     * Detect billboard type from current page
     */
    detectBillboardType() {
        if (window.location.pathname.includes('templated-billboard')) {
            return 'templated';
        } else if (window.location.pathname.includes('custom-billboard')) {
            return 'custom';
        }
        return 'custom';
    }
    
    /**
     * Save image to server
     */
    async saveImageToServer(imageData, orderData) {
        // Determine the correct path based on current location
        let apiPath = '../shared/save-billboard-image.php';

        // If we're at root level (like test pages), use direct path
        if (window.location.pathname === '/' || window.location.pathname.endsWith('.html')) {
            apiPath = 'customer/shared/save-billboard-image.php';
        }

        const response = await fetch(apiPath, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                imageData: imageData,
                billboardType: orderData.billboardType,
                customerEmail: orderData.customerEmail,
                customerName: orderData.customerName,
                templateId: orderData.templateId,
                designData: orderData.designData
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Server error: ${response.status} - ${response.statusText}. Response: ${errorText.substring(0, 200)}`);
        }

        const responseText = await response.text();
        let result;

        try {
            result = JSON.parse(responseText);
        } catch (parseError) {
            console.error('Failed to parse server response as JSON:', responseText.substring(0, 500));
            throw new Error(`Server returned invalid JSON. Response starts with: ${responseText.substring(0, 100)}`);
        }

        if (!result.success) {
            throw new Error(result.error || 'Failed to save image');
        }

        return result;
    }
    
    /**
     * Show progress message
     */
    showProgress(message, type = 'info') {
        // Try to find existing progress element
        let progressEl = document.getElementById('imageGenerationProgress');
        
        if (!progressEl) {
            // Create progress element
            progressEl = document.createElement('div');
            progressEl.id = 'imageGenerationProgress';
            progressEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#dc3545' : '#28a745'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                max-width: 300px;
            `;
            document.body.appendChild(progressEl);
        }
        
        progressEl.textContent = message;
        progressEl.style.background = type === 'error' ? '#dc3545' : '#28a745';
        progressEl.style.display = 'block';
    }
    
    /**
     * Hide progress message
     */
    hideProgress() {
        const progressEl = document.getElementById('imageGenerationProgress');
        if (progressEl) {
            progressEl.style.display = 'none';
        }
    }
    
    /**
     * Get last generated image info
     */
    getLastGeneratedImage() {
        return this.lastGeneratedImage;
    }
    
    /**
     * Check if generation is in progress
     */
    isGenerationInProgress() {
        return this.isGenerating;
    }
}

// Create global instance
window.highQualityImageGenerator = new HighQualityImageGenerator();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HighQualityImageGenerator;
}
