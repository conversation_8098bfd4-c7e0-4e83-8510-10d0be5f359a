/**
 * Fabric.js Canvas Exporter
 * High-quality export utility for Fabric.js canvases with proper error handling and fallbacks
 */

class FabricCanvasExporter {
    constructor() {
        this.isExporting = false;
        this.lastExportResult = null;
        
        // 🔥 UPGRADED: Ultra high-quality export settings for 3200×1600+ resolution
        this.exportSettings = {
            // Ultra high quality settings for 3200×1600+ output (target resolution)
            highQuality: {
                format: 'png',
                quality: 1.0,
                multiplier: 4, // 🔥 4x on 1600×800 base = 6400×3200 (exceeds target)
                enableRetinaScaling: true
            },

            // High quality for reliable processing
            standardQuality: {
                format: 'png',
                quality: 0.98,
                multiplier: 3, // 🔥 3x on 1600×800 base = 4800×2400 (exceeds target)
                enableRetinaScaling: true
            },

            // Fallback quality ensuring minimum target resolution
            fallbackQuality: {
                format: 'png',
                quality: 0.95,
                multiplier: 2, // 🔥 2x on 1600×800 base = 3200×1600 (meets target exactly)
                enableRetinaScaling: true
            }
        };
    }
    
    /**
     * Find active Fabric.js canvas instance
     */
    findFabricCanvas() {
        // Check for global canvas instances
        if (window.canvas && window.canvas.toDataURL) {
            return window.canvas;
        }

        // Check for canvas in CF7 editors
        if (window.cf7Editors && window.cf7Editors.length > 0) {
            for (const editor of window.cf7Editors) {
                if (editor.canvas && editor.canvas.toDataURL) {
                    return editor.canvas;
                }
            }
        }

        // Check for canvas in billboard editor modules
        if (window.billboardEditor && window.billboardEditor.modules) {
            const canvasManager = window.billboardEditor.modules.canvasManager;
            if (canvasManager && canvasManager.getCanvas) {
                const canvas = canvasManager.getCanvas();
                if (canvas && canvas.toDataURL) {
                    return canvas;
                }
            }
        }

        // Check for templated billboard canvas
        if (window.canvasManager && window.canvasManager.canvas) {
            return window.canvasManager.canvas;
        }

        // Check all Fabric.js instances and match with canvas elements
        if (window.fabric && window.fabric.Canvas && window.fabric.Canvas.getInstances) {
            const fabricInstances = window.fabric.Canvas.getInstances();
            console.log(`🔍 Fabric Exporter: Found ${fabricInstances.length} Fabric.js instances`);

            // Check common canvas IDs
            const canvasIds = ['fabricCanvas', 'billboard-canvas', 'canvas'];

            for (const canvasId of canvasIds) {
                const canvasElement = document.getElementById(canvasId);
                if (canvasElement) {
                    console.log(`🔍 Fabric Exporter: Found canvas element with ID: ${canvasId}`);

                    // Find matching Fabric instance
                    for (let i = 0; i < fabricInstances.length; i++) {
                        const fabricCanvas = fabricInstances[i];
                        if (fabricCanvas.lowerCanvasEl === canvasElement ||
                            fabricCanvas.upperCanvasEl === canvasElement ||
                            fabricCanvas.getElement() === canvasElement) {
                            console.log(`✅ Fabric Exporter: Found matching Fabric.js canvas for ${canvasId}`);
                            return fabricCanvas;
                        }
                    }
                }
            }

            // If no specific match, return the first available Fabric instance
            if (fabricInstances.length > 0) {
                console.log('✅ Fabric Exporter: Using first available Fabric.js instance');
                return fabricInstances[0];
            }
        }

        return null;
    }
    
    /**
     * Export canvas with high quality settings
     */
    async exportHighQuality(canvas = null, options = {}) {
        if (this.isExporting) {
            throw new Error('Export already in progress');
        }
        
        try {
            this.isExporting = true;
            
            // Find canvas if not provided
            if (!canvas) {
                canvas = this.findFabricCanvas();
                if (!canvas) {
                    throw new Error('No Fabric.js canvas found');
                }
            }
            
            // Validate canvas
            if (!canvas.toDataURL) {
                throw new Error('Invalid canvas object - missing toDataURL method');
            }
            
            console.log('Starting high-quality Fabric.js canvas export...');
            
            // Try high quality first
            let exportResult = await this.tryExportWithSettings(canvas, 'highQuality', options);
            
            if (exportResult.success) {
                this.lastExportResult = exportResult;
                return exportResult;
            }
            
            // Fallback to standard quality
            console.warn('High quality export failed, trying standard quality...');
            exportResult = await this.tryExportWithSettings(canvas, 'standardQuality', options);
            
            if (exportResult.success) {
                this.lastExportResult = exportResult;
                return exportResult;
            }
            
            // Final fallback
            console.warn('Standard quality export failed, trying fallback quality...');
            exportResult = await this.tryExportWithSettings(canvas, 'fallbackQuality', options);
            
            this.lastExportResult = exportResult;
            return exportResult;
            
        } finally {
            this.isExporting = false;
        }
    }
    
    /**
     * Try export with specific quality settings
     */
    async tryExportWithSettings(canvas, qualityLevel, userOptions = {}) {
        try {
            const settings = { ...this.exportSettings[qualityLevel], ...userOptions };

            console.log(`Attempting export with ${qualityLevel} settings:`, settings);

            // Clean the canvas before export (remove UI elements)
            const cleanupState = this.prepareCanvasForCleanExport(canvas);

            try {
                // 🔥 FIXED: Convert settings to proper Fabric.js toDataURL format
                const fabricExportOptions = {
                    format: `image/${settings.format}`,
                    quality: settings.quality,
                    multiplier: settings.multiplier,
                    enableRetinaScaling: settings.enableRetinaScaling
                };

                console.log(`🎯 Exporting with Fabric.js options:`, fabricExportOptions);
                console.log(`   Expected output resolution: ${canvas.getWidth() * settings.multiplier}×${canvas.getHeight() * settings.multiplier}`);

                // Export image data using correct Fabric.js syntax
                const imageData = canvas.toDataURL(fabricExportOptions);

                // Validate image data
                if (!imageData || !imageData.startsWith('data:image/')) {
                    throw new Error('Invalid image data generated');
                }

                console.log(`✅ High-resolution export successful - Data URL length: ${imageData.length} chars`);

                return await this.processExportResult(canvas, imageData, qualityLevel, settings);

            } finally {
                // Always restore canvas state
                this.restoreCanvasAfterExport(canvas, cleanupState);
            }

        } catch (error) {
            console.error(`Export failed with ${qualityLevel}:`, error);
            return {
                success: false,
                error: error.message,
                qualityLevel: qualityLevel
            };
        }
    }
    
    /**
     * Export for checkout (optimized for payment processing)
     */
    async exportForCheckout(canvas = null) {
        try {
            const result = await this.exportHighQuality(canvas, {
                // 🔥 UPGRADED: Ultra high quality for checkout - ensures 3200×1600+ resolution
                multiplier: 4, // 🔥 4x on 1600×800 base = 6400×3200 (exceeds target)
                quality: 1.0   // 🔥 Maximum quality for checkout
            });
            
            if (result.success) {
                // Prepare data for checkout system
                return {
                    imageData: result.imageData,
                    canvasJSON: result.canvasJSON,
                    dimensions: result.dimensions,
                    captureMethod: 'fabric_checkout_optimized',
                    qualityLevel: result.qualityLevel,
                    fileSize: result.fileSize,
                    timestamp: result.timestamp
                };
            } else {
                throw new Error(result.error || 'Export failed');
            }
            
        } catch (error) {
            console.error('Checkout export failed:', error);
            throw error;
        }
    }
    
    /**
     * Export for download (maximum quality)
     */
    async exportForDownload(canvas = null, filename = 'billboard-design') {
        try {
            const result = await this.exportHighQuality(canvas);
            
            if (result.success) {
                // Create download link
                const link = document.createElement('a');
                link.download = `${filename}.png`;
                link.href = result.imageData;
                
                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                return result;
            } else {
                throw new Error(result.error || 'Export failed');
            }
            
        } catch (error) {
            console.error('Download export failed:', error);
            throw error;
        }
    }
    
    /**
     * Get canvas information without exporting
     */
    getCanvasInfo(canvas = null) {
        if (!canvas) {
            canvas = this.findFabricCanvas();
        }
        
        if (!canvas) {
            return null;
        }
        
        return {
            width: canvas.width,
            height: canvas.height,
            zoom: canvas.getZoom(),
            objectCount: canvas.getObjects().length,
            hasBackground: !!canvas.backgroundImage,
            backgroundColor: canvas.backgroundColor
        };
    }
    
    /**
     * Check if export is currently in progress
     */
    isExportInProgress() {
        return this.isExporting;
    }
    
    /**
     * Get last export result
     */
    getLastExportResult() {
        return this.lastExportResult;
    }

    /**
     * Prepare canvas for clean export by removing UI elements
     */
    prepareCanvasForCleanExport(canvas) {
        const cleanupState = {
            activeObject: canvas.getActiveObject(),
            activeSelection: canvas.getActiveSelection(),
            interactive: canvas.interactive,
            selection: canvas.selection,
            hoverCursor: canvas.hoverCursor,
            moveCursor: canvas.moveCursor,
            defaultCursor: canvas.defaultCursor,
            objectsState: []
        };

        // Store original object states and disable interactivity
        canvas.forEachObject((obj) => {
            const objState = {
                object: obj,
                selectable: obj.selectable,
                evented: obj.evented,
                hasControls: obj.hasControls,
                hasBorders: obj.hasBorders,
                hoverCursor: obj.hoverCursor,
                moveCursor: obj.moveCursor
            };
            cleanupState.objectsState.push(objState);

            // Disable all interactive features
            obj.selectable = false;
            obj.evented = false;
            obj.hasControls = false;
            obj.hasBorders = false;
            obj.hoverCursor = 'default';
            obj.moveCursor = 'default';
        });

        // Disable canvas interactivity
        canvas.interactive = false;
        canvas.selection = false;
        canvas.hoverCursor = 'default';
        canvas.moveCursor = 'default';
        canvas.defaultCursor = 'default';

        // Clear any active selections
        canvas.discardActiveObject();

        // Force canvas re-render to apply changes
        canvas.renderAll();

        console.log('✅ Canvas prepared for clean export (UI elements disabled)');
        return cleanupState;
    }

    /**
     * Restore canvas state after export
     */
    restoreCanvasAfterExport(canvas, cleanupState) {
        try {
            // Restore canvas properties
            canvas.interactive = cleanupState.interactive;
            canvas.selection = cleanupState.selection;
            canvas.hoverCursor = cleanupState.hoverCursor;
            canvas.moveCursor = cleanupState.moveCursor;
            canvas.defaultCursor = cleanupState.defaultCursor;

            // Restore object states
            cleanupState.objectsState.forEach((objState) => {
                const obj = objState.object;
                obj.selectable = objState.selectable;
                obj.evented = objState.evented;
                obj.hasControls = objState.hasControls;
                obj.hasBorders = objState.hasBorders;
                obj.hoverCursor = objState.hoverCursor;
                obj.moveCursor = objState.moveCursor;
            });

            // Restore active selection if it existed
            if (cleanupState.activeObject) {
                canvas.setActiveObject(cleanupState.activeObject);
            }

            // Force canvas re-render to apply restored state
            canvas.renderAll();

            console.log('✅ Canvas state restored after export');

        } catch (error) {
            console.error('Error restoring canvas state:', error);
            // Force a basic restore
            canvas.interactive = true;
            canvas.selection = true;
            canvas.forEachObject((obj) => {
                obj.selectable = true;
                obj.evented = true;
                obj.hasControls = true;
                obj.hasBorders = true;
            });
            canvas.renderAll();
        }
    }

    /**
     * Process export result with validation and metadata
     */
    async processExportResult(canvas, imageData, qualityLevel, settings) {
        // Check file size (approximate)
        const sizeInBytes = (imageData.length * 0.75); // Base64 to binary approximation
        const sizeInMB = sizeInBytes / (1024 * 1024);

        console.log(`✅ Clean export successful - Size: ${sizeInMB.toFixed(2)}MB (no UI elements)`);

        // Also capture canvas JSON for reconstruction (with clean state)
        const canvasJSON = canvas.toJSON(['selectable', 'evented', 'id', 'name']);

        return {
            success: true,
            imageData: imageData,
            canvasJSON: canvasJSON,
            dimensions: {
                width: canvas.getWidth(),
                height: canvas.getHeight()
            },
            fileSize: {
                bytes: sizeInBytes,
                mb: sizeInMB
            },
            qualityLevel: qualityLevel,
            settings: settings,
            cleanExport: true, // Flag indicating UI elements were removed
            timestamp: new Date().toISOString()
        };
    }
}

// Create global instance
window.fabricCanvasExporter = new FabricCanvasExporter();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FabricCanvasExporter;
}
