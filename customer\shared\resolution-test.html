<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Ultra High Resolution Test - 3200×1600+ Validation</title>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            color: #1f2937;
            margin-top: 0;
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        #testCanvas {
            border: 4px solid #000;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .results {
            margin: 20px 0;
            padding: 15px;
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 6px;
        }
        
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #dc2626;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
        }
        
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .progress {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #2563eb;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Ultra High Resolution Test</h1>
            <p>Comprehensive validation for 3200×1600+ pixel billboard export</p>
        </div>

        <div class="test-section">
            <h3>📊 System Information</h3>
            <div class="info-grid" id="systemInfo">
                <!-- System info will be populated here -->
            </div>
        </div>

        <div class="test-section">
            <h3>🎨 Canvas Test</h3>
            <div class="canvas-container">
                <canvas id="testCanvas"></canvas>
            </div>
            <div class="controls">
                <button onclick="initializeCanvas()">Initialize Canvas</button>
                <button onclick="addTestContent()">Add Test Content</button>
                <button onclick="testExportResolution()">Test Export Resolution</button>
                <button onclick="testCheckoutExport()">Test Checkout Export</button>
                <button onclick="downloadTestImage()">Download Test Image</button>
            </div>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 Test Results</h3>
            <div id="testResults">
                <p>Click "Initialize Canvas" to start testing...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Export Analysis</h3>
            <div id="exportAnalysis">
                <p>Export analysis will appear here after testing...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Debug Log</h3>
            <pre id="debugLog">Debug information will appear here...</pre>
        </div>
    </div>

    <!-- Load Fabric.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <!-- Load our enhanced exporters -->
    <script src="fabric-canvas-exporter.js"></script>
    <script src="high-quality-image-generator.js"></script>

    <script>
        let canvas = null;
        let testResults = [];
        
        // Initialize system information
        function initializeSystemInfo() {
            const systemInfo = document.getElementById('systemInfo');
            const devicePixelRatio = window.devicePixelRatio || 1;
            const screenResolution = `${screen.width}×${screen.height}`;
            const viewportSize = `${window.innerWidth}×${window.innerHeight}`;
            
            systemInfo.innerHTML = `
                <div class="info-card">
                    <h4>Device Information</h4>
                    <p><strong>Device Pixel Ratio:</strong> ${devicePixelRatio}</p>
                    <p><strong>Screen Resolution:</strong> ${screenResolution}</p>
                    <p><strong>Viewport Size:</strong> ${viewportSize}</p>
                </div>
                <div class="info-card">
                    <h4>Browser Support</h4>
                    <p><strong>Fabric.js:</strong> ${typeof fabric !== 'undefined' ? '✅ Loaded' : '❌ Not Found'}</p>
                    <p><strong>Canvas Support:</strong> ${document.createElement('canvas').getContext ? '✅ Supported' : '❌ Not Supported'}</p>
                    <p><strong>toDataURL:</strong> ${HTMLCanvasElement.prototype.toDataURL ? '✅ Available' : '❌ Not Available'}</p>
                </div>
                <div class="info-card">
                    <h4>Target Resolution</h4>
                    <p><strong>Minimum Target:</strong> 3200×1600 pixels</p>
                    <p><strong>Expected Output:</strong> 6400×3200 pixels</p>
                    <p><strong>Base Canvas:</strong> 1600×800 pixels</p>
                    <p><strong>Multiplier:</strong> 4x</p>
                </div>
            `;
        }
        
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.textContent += `[${timestamp}] ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        function showResults(results) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = results;
        }
        
        function showExportAnalysis(analysis) {
            const analysisDiv = document.getElementById('exportAnalysis');
            analysisDiv.innerHTML = analysis;
        }
        
        // Initialize canvas with high resolution
        function initializeCanvas() {
            log('🔄 Initializing high-resolution canvas...');
            updateProgress(10);

            try {
                // Create canvas with our new high-resolution dimensions
                canvas = new fabric.Canvas('testCanvas', {
                    width: 1600,  // 🔥 New high-resolution base width
                    height: 800,  // 🔥 New high-resolution base height
                    backgroundColor: '#ffffff',
                    selection: true,
                    preserveObjectStacking: true,
                    enableRetinaScaling: true
                });

                log(`✅ Canvas created: ${canvas.getWidth()}×${canvas.getHeight()} pixels`);
                updateProgress(30);

                showResults(`
                    <div class="results">
                        <h4><span class="status-indicator status-success"></span>Canvas Initialized Successfully</h4>
                        <p><strong>Canvas Dimensions:</strong> ${canvas.getWidth()}×${canvas.getHeight()} pixels</p>
                        <p><strong>Expected Export (4x):</strong> ${canvas.getWidth() * 4}×${canvas.getHeight() * 4} pixels</p>
                        <p><strong>Meets Target:</strong> ${canvas.getWidth() * 4 >= 3200 && canvas.getHeight() * 4 >= 1600 ? '✅ YES' : '❌ NO'}</p>
                    </div>
                `);

            } catch (error) {
                log(`❌ Canvas initialization failed: ${error.message}`);
                showResults(`
                    <div class="results error">
                        <h4><span class="status-indicator status-error"></span>Canvas Initialization Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `);
            }
        }

        function addTestContent() {
            if (!canvas) {
                log('❌ Canvas not initialized');
                return;
            }

            log('🔄 Adding test content to canvas...');
            updateProgress(50);

            try {
                // Add background
                const rect = new fabric.Rect({
                    left: 0,
                    top: 0,
                    width: canvas.getWidth(),
                    height: canvas.getHeight(),
                    fill: 'linear-gradient(45deg, #667eea, #764ba2)',
                    selectable: false
                });
                canvas.add(rect);

                // Add test text
                const text = new fabric.Text('🔥 ULTRA HIGH RESOLUTION TEST\n3200×1600+ PIXELS', {
                    left: canvas.getWidth() / 2,
                    top: canvas.getHeight() / 2,
                    originX: 'center',
                    originY: 'center',
                    fontSize: 48,
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    textAlign: 'center',
                    shadow: 'rgba(0,0,0,0.5) 2px 2px 4px'
                });
                canvas.add(text);

                // Add resolution indicator
                const resolutionText = new fabric.Text(`Base: ${canvas.getWidth()}×${canvas.getHeight()} | Target: ${canvas.getWidth() * 4}×${canvas.getHeight() * 4}`, {
                    left: 20,
                    top: canvas.getHeight() - 40,
                    fontSize: 24,
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                canvas.add(resolutionText);

                canvas.renderAll();
                log('✅ Test content added successfully');
                updateProgress(70);

            } catch (error) {
                log(`❌ Failed to add test content: ${error.message}`);
            }
        }

        async function testExportResolution() {
            if (!canvas) {
                log('❌ Canvas not initialized');
                return;
            }

            log('🔄 Testing export resolution...');
            updateProgress(80);

            try {
                // Test different multiplier values
                const multipliers = [2, 3, 4, 6, 8];
                const results = [];

                for (const multiplier of multipliers) {
                    log(`Testing ${multiplier}x multiplier...`);

                    const startTime = performance.now();
                    const dataURL = canvas.toDataURL({
                        format: 'image/png',
                        quality: 1.0,
                        multiplier: multiplier
                    });
                    const endTime = performance.now();

                    const expectedWidth = canvas.getWidth() * multiplier;
                    const expectedHeight = canvas.getHeight() * multiplier;
                    const fileSize = Math.round(dataURL.length * 0.75 / 1024); // Approximate KB
                    const exportTime = Math.round(endTime - startTime);

                    results.push({
                        multiplier,
                        expectedWidth,
                        expectedHeight,
                        fileSize,
                        exportTime,
                        meetsTarget: expectedWidth >= 3200 && expectedHeight >= 1600,
                        dataURL: dataURL.substring(0, 100) + '...' // Truncated for display
                    });

                    log(`✅ ${multiplier}x: ${expectedWidth}×${expectedHeight} (${fileSize}KB, ${exportTime}ms)`);
                }

                updateProgress(100);

                // Display results
                let resultsHTML = '<div class="results"><h4>Export Resolution Test Results</h4>';
                results.forEach(result => {
                    const statusClass = result.meetsTarget ? 'status-success' : 'status-warning';
                    resultsHTML += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <p><span class="status-indicator ${statusClass}"></span>
                            <strong>${result.multiplier}x Multiplier:</strong> ${result.expectedWidth}×${result.expectedHeight} pixels</p>
                            <p><strong>File Size:</strong> ~${result.fileSize}KB | <strong>Export Time:</strong> ${result.exportTime}ms</p>
                            <p><strong>Meets Target:</strong> ${result.meetsTarget ? '✅ YES' : '❌ NO'}</p>
                        </div>
                    `;
                });
                resultsHTML += '</div>';

                showResults(resultsHTML);

                // Show analysis
                const bestResult = results.find(r => r.meetsTarget && r.fileSize < 10000) || results[results.length - 1];
                showExportAnalysis(`
                    <div class="results">
                        <h4>📊 Export Analysis</h4>
                        <p><strong>Recommended Setting:</strong> ${bestResult.multiplier}x multiplier</p>
                        <p><strong>Output Resolution:</strong> ${bestResult.expectedWidth}×${bestResult.expectedHeight} pixels</p>
                        <p><strong>Performance:</strong> ${bestResult.exportTime}ms export time</p>
                        <p><strong>File Size:</strong> ~${bestResult.fileSize}KB</p>
                        <p><strong>Quality Assessment:</strong> ${bestResult.meetsTarget ? '🔥 Exceeds target resolution!' : '⚠️ Below target resolution'}</p>
                    </div>
                `);

            } catch (error) {
                log(`❌ Export test failed: ${error.message}`);
                showResults(`
                    <div class="results error">
                        <h4><span class="status-indicator status-error"></span>Export Test Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `);
            }
        }

        async function testCheckoutExport() {
            if (!canvas) {
                log('❌ Canvas not initialized');
                return;
            }

            log('🔄 Testing checkout export process...');

            try {
                // Test using our enhanced fabric canvas exporter
                if (window.fabricCanvasExporter) {
                    log('Using enhanced Fabric Canvas Exporter...');
                    const result = await window.fabricCanvasExporter.exportForCheckout(canvas);

                    log(`✅ Checkout export successful`);
                    log(`Resolution: ${result.dimensions.width}×${result.dimensions.height}`);
                    log(`File size: ${result.fileSize.mb.toFixed(2)}MB`);
                    log(`Quality level: ${result.qualityLevel}`);

                    showExportAnalysis(`
                        <div class="results">
                            <h4>🛒 Checkout Export Test Results</h4>
                            <p><strong>Export Method:</strong> Enhanced Fabric Canvas Exporter</p>
                            <p><strong>Base Resolution:</strong> ${result.dimensions.width}×${result.dimensions.height} pixels</p>
                            <p><strong>Quality Level:</strong> ${result.qualityLevel}</p>
                            <p><strong>File Size:</strong> ${result.fileSize.mb.toFixed(2)}MB (${result.fileSize.bytes} bytes)</p>
                            <p><strong>Capture Method:</strong> ${result.captureMethod}</p>
                            <p><strong>Clean Export:</strong> ${result.cleanExport ? '✅ Yes' : '❌ No'}</p>
                        </div>
                    `);

                } else {
                    log('⚠️ Enhanced exporter not available, using fallback...');

                    // Fallback test
                    const dataURL = canvas.toDataURL({
                        format: 'image/png',
                        quality: 1.0,
                        multiplier: 4
                    });

                    const expectedWidth = canvas.getWidth() * 4;
                    const expectedHeight = canvas.getHeight() * 4;
                    const fileSize = Math.round(dataURL.length * 0.75 / 1024);

                    log(`✅ Fallback export: ${expectedWidth}×${expectedHeight} (~${fileSize}KB)`);

                    showExportAnalysis(`
                        <div class="results">
                            <h4>🛒 Checkout Export Test Results (Fallback)</h4>
                            <p><strong>Export Method:</strong> Direct Canvas toDataURL</p>
                            <p><strong>Resolution:</strong> ${expectedWidth}×${expectedHeight} pixels</p>
                            <p><strong>File Size:</strong> ~${fileSize}KB</p>
                            <p><strong>Meets Target:</strong> ${expectedWidth >= 3200 && expectedHeight >= 1600 ? '✅ YES' : '❌ NO'}</p>
                        </div>
                    `);
                }

            } catch (error) {
                log(`❌ Checkout export test failed: ${error.message}`);
                showExportAnalysis(`
                    <div class="results error">
                        <h4><span class="status-indicator status-error"></span>Checkout Export Test Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `);
            }
        }

        function downloadTestImage() {
            if (!canvas) {
                log('❌ Canvas not initialized');
                return;
            }

            log('🔄 Generating download image...');

            try {
                // Generate ultra high-resolution image
                const dataURL = canvas.toDataURL({
                    format: 'image/png',
                    quality: 1.0,
                    multiplier: 4  // 4x on 1600×800 = 6400×3200
                });

                // Create download link
                const link = document.createElement('a');
                link.download = `ultra-high-res-test-${canvas.getWidth() * 4}x${canvas.getHeight() * 4}.png`;
                link.href = dataURL;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                const fileSize = Math.round(dataURL.length * 0.75 / 1024);
                log(`✅ Download initiated: ${canvas.getWidth() * 4}×${canvas.getHeight() * 4} pixels (~${fileSize}KB)`);

                showResults(`
                    <div class="results">
                        <h4><span class="status-indicator status-success"></span>Download Generated</h4>
                        <p><strong>Resolution:</strong> ${canvas.getWidth() * 4}×${canvas.getHeight() * 4} pixels</p>
                        <p><strong>File Size:</strong> ~${fileSize}KB</p>
                        <p><strong>Format:</strong> PNG</p>
                        <p><strong>Quality:</strong> 100%</p>
                    </div>
                `);

            } catch (error) {
                log(`❌ Download generation failed: ${error.message}`);
                showResults(`
                    <div class="results error">
                        <h4><span class="status-indicator status-error"></span>Download Failed</h4>
                        <p>${error.message}</p>
                    </div>
                `);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystemInfo();
            log('🔥 Ultra High Resolution Test initialized');
            log('Target: 3200×1600+ pixel output resolution');
            log('New base canvas: 1600×800 pixels');
            log('Expected output with 4x multiplier: 6400×3200 pixels');
        });
    </script>
</body>
</html>
